# Implementação do Sistema de Referências - e-Contas (SAPC)

## Resumo da Implementação

Foi implementado o gatilho para gerar automaticamente a lista de referências quando todos os formulários de uma análise estão concluídos.

## Arquivos Modificados

### 1. `app/Services/SapcService.php`
- **Função adicionada**: `gerarReferencias(Analise $analise)`
- **Refatoração**: Removidos métodos duplicados, agora usa o trait `SapcParseText`

**Funcionalidades:**
- Busca o formulário de referências da análise (onde `referencias = true`)
- Usa `extrairReferencias()` e `gerarListaReferenciasHtml()` do trait `SapcParseText`
- Substitui a variável `$_marcador{Referencia}` no formulário de referências
- Salva o formulário atualizado

### 2. `app/Models/Sapc/AnaliseFormulario.php`
- **Modificação**: Evento `saved()` no método `booted()`

**Funcionalidades:**
- Quando todos os formulários estão finalizados (tempestivos ou intempestivos), chama automaticamente `gerarReferencias()`
- Mantém a lógica original de atualização do status da análise

### 3. `app/Http/Controllers/SapcController.php`
- **Função adicionada**: `testarGerarReferencias(Analise $analise)`

**Funcionalidades:**
- Endpoint para testar manualmente a geração de referências
- Retorna JSON com status de sucesso/erro

### 4. `routes/api.php`
- **Rota adicionada**: `POST /sapc/analise/{analise}/gerar-referencias`

### 5. `app/Traits/SapcParseText.php`
- **Métodos centralizados**: Todos os métodos de referências ficaram no trait
- **Funções**: `extrairReferencias()`, `extrairReferenciasDotexto()`, `gerarListaReferenciasHtml()`
- **Eliminação de duplicação**: Código redundante foi removido do `SapcService`
- **Simplificação**: Removido campo `'numero'` desnecessário (numeração automática do `<ol>`)
- **Otimização**: Array de referências simplificado para array simples de strings

## Como Funciona

### Gatilho Automático
1. Usuário clica no botão "Concluir" (azul) em um formulário
2. O formulário é salvo com status "finalizado"
3. O evento `saved()` do modelo `AnaliseFormulario` é disparado
4. O sistema verifica se todos os formulários da análise estão finalizados
5. Se sim, chama automaticamente `gerarReferencias()`
6. A função busca todas as referências marcadas e atualiza o formulário de referências

### Marcação de Referências
Para marcar um texto como referência no CKEditor:
1. Selecione o texto que contém a referência
2. Aplique o estilo "Referências" (que adiciona `class="econtas-referencia"`)
3. O texto será automaticamente incluído na lista quando todos os formulários estiverem concluídos

### Exemplo de Marcação HTML
```html
<span class="econtas-referencia">SECRETARIA DO TESOURO NACIONAL (Brasil). Manual de Contabilidade Aplicada ao Setor Público: MCASP. 10. ed. Brasília: STN, 2023.</span>
```

### Resultado na Lista de Referências
```html
<ol style="font-family: Times New Roman; font-size: 12pt; color: black;">
    <li>SECRETARIA DO TESOURO NACIONAL (Brasil). Manual de Contabilidade Aplicada ao Setor Público: MCASP. 10. ed. Brasília: STN, 2023.</li>
    <li>Painel das Emendas Parlamentares Individuais e de Bancada. Disponível em: https://www.tesourotransparente.gov.br/consultas/painel-das-emendas-parlamentares-individuais-e-de-bancada Acesso em: 01/01/2025</li>
</ol>
```

## Teste Manual

Para testar a funcionalidade manualmente, você pode usar o endpoint:

```bash
POST /sapc/analise/{id_da_analise}/gerar-referencias
```

### Estrutura dos Dados

**Array de referências (simplificado):**
```php
[
    'SECRETARIA DO TESOURO NACIONAL (Brasil). Manual de Contabilidade Aplicada ao Setor Público: MCASP. 10. ed. Brasília: STN, 2023.',
    'Painel das Emendas Parlamentares Individuais e de Bancada. Disponível em: https://www.tesourotransparente.gov.br/consultas/painel-das-emendas-parlamentares-individuais-e-de-bancada Acesso em: 01/01/2025'
]
```

**Exemplo de resposta de sucesso:**
```json
{
    "success": true,
    "message": "Referências geradas com sucesso!"
}
```

**Exemplo de resposta de erro:**
```json
{
    "success": false,
    "message": "Formulário de referências não encontrado para a análise: 123"
}
```

## Logs

O sistema gera logs informativos em `storage/logs/laravel.log`:
- Quando referências são geradas com sucesso
- Quando não há formulário de referências
- Quando não há referências marcadas
- Quando ocorrem erros

## Pré-requisitos

1. **Formulário de Referências**: Deve existir um modelo de formulário com `referencias = true`
2. **Seeder**: Execute o `SapcReferenciasSeeder` para criar o modelo de formulário de referências
3. **Estilo CKEditor**: O estilo "Referências" deve estar configurado no CKEditor (já configurado em `config/nova-ckeditor.php`)

## Próximos Passos

1. **Teste em ambiente de desenvolvimento**: Criar uma análise com formulários e testar o fluxo completo
2. **Validação**: Verificar se as referências são extraídas corretamente
3. **Refinamentos**: Ajustar formatação ou comportamento conforme necessário
4. **Documentação para usuários**: Criar guia de como marcar referências no CKEditor

## Observações Importantes

- **Durante a criação da análise**: A variável `$_marcador{Referencia}` é **preservada** no formulário de referências (não é processada no `parseTexto()` inicial)
- **Durante a edição**: A variável permanece intacta até que todos os formulários sejam finalizados
- **Quando todos os formulários estão finalizados**: A variável é sempre substituída pelo gatilho automático (mesmo que por lista vazia)
- A formatação segue as especificações: Times New Roman, 12pt, cor preta
- O sistema é tolerante a erros e registra problemas nos logs sem quebrar o fluxo principal
