<?php

namespace App\Observers\Sapc;

use App\Models\Sapc\GerarAnalise;
use App\Models\Sapc\ModeloAnalise;
use App\Models\TipoUnidade;
use App\Models\UnidadeGestora;
use App\Services\SapcService;

class GerarAnaliseObserver
{
    private $sapcService;

    public function __construct(SapcService $sapcService)
    {
        $this->sapcService = $sapcService;
    }

    /**
     * Handle the GerarAnalise "creating" event.
     *
     * @return void
     */
    public function creating(GerarAnalise $gerar_analise)
    {
        $tipoUnidade = TipoUnidade::find($gerar_analise->tipo_unidade_id);
        $modeloAnalise = ModeloAnalise::find($gerar_analise->modelo_analise_id);
        $unidadesGestoras = UnidadeGestora::when(
            request()->isNotFilled('unidade_gestora_id'), function ($query) {
                return $query->whereHas('tipoUnidade', function ($query) {
                    $query->whereId(request()->input('tipo_unidade_id'));
                });
            }, function ($query) {
                return $query->whereIn('id', request()->input('unidade_gestora_id'));
            }
        )->get();

        $analises = $this->sapcService
            ->gerarAnalisesPorModelo(
                $unidadesGestoras,
                $gerar_analise->exercicio,
                $modeloAnalise->id
            );

        // Processar referências dos modelos de formulários para cada análise criada
        foreach ($analises as $analise) {
            if ($analise) {
                try {
                    $this->sapcService->gerarReferencias($analise);
                    \Log::info('Referências processadas para a análise criada: ' . $analise->id);
                } catch (\Exception $e) {
                    \Log::warning('Erro ao processar referências na criação da análise ' . $analise->id . ': ' . $e->getMessage());
                }
            }
        }

        $analises = array_filter($analises);

        $totalAnalises = count($analises);

        $gerar_analise->descricao = $totalAnalises
            ? "{$totalAnalises} analises foram criadas. São elas os IDs: ".collect($analises)->pluck('id')->implode(',')."\n"
            : 'Nenhuma analise foi criada.';

        $gerar_analise->descricao .= 'Tipo Unidade: '.$tipoUnidade->description."\n";
        $gerar_analise->descricao .= 'Unidades Gestoras: '.$unidadesGestoras->pluck('nome')->implode(', ')."\n";
        $gerar_analise->descricao .= 'Modelo Analise: '.$modeloAnalise->nome."\n";
        $gerar_analise->descricao .= 'Exercicio: '.$gerar_analise->exercicio."\n";

        unset(
            $gerar_analise->tipo_unidade_id,
            $gerar_analise->unidade_gestora_id,
            $gerar_analise->modelo_analise_id,
            $gerar_analise->exercicio
        );
    }
}
