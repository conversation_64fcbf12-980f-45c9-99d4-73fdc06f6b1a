<?php

namespace App\Traits;

use App\Models\Sapc\Analise;
use App\Models\Sapc\Parametrizacao;
use setasign\Fpdi\Fpdi;
use Smalot\PdfParser\Parser;
use Spatie\Browsershot\Browsershot;

trait GenerateAnalisePDFTrait
{
    const PDF_TOP_MARGIN = 0;

    const PDF_RIGHT_MARGIN = 0;

    const PDF_BOTTOM_MARGIN = 0;

    const PDF_LEFT_MARGIN = 0;

    const PDF_FORMAT = 'A4';

    const PDF_MEDIA_PORTRAIT = 'print';

    const PDF_MEDIA_LANDSCAPE = 'screen';

    private function extractHeadings(string $html): array
    {
        $headings = [];
        $dom = new \DOMDocument;
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        $xpath = new \DOMXPath($dom);
        foreach (['h1', 'h2', 'h3', 'h4', 'h5'] as $tag) {
            $nodes = $xpath->query("//{$tag}");
            foreach ($nodes as $node) {
                $level = (int) substr($tag, 1, 1);

                $normalized = preg_replace(
                    '/\s+/u',
                    ' ',
                    str_replace("\u{00A0}", ' ', $node->textContent)
                );

                $title = trim($normalized);

                if ($title !== '') {
                    $headings[] = [
                        'level' => $level == 1 ? 1 : 2,
                        'title' => $title,
                    ];
                }
            }
        }

        return $headings;
    }

    private function buildFormsData(Analise $analise): array
    {
        $cabecalhoPadrao = Parametrizacao::getValor('cabecalho_padrao');
        $cssRenderPath = public_path('/sapc/pdf/render/sapc-pdf-render.css');

        $formsData = [];
        foreach ($analise->analisesFormularios as $formulario) {
            $html = view('pdf.sapc.analise.formularios_novo', [
                'formulario' => $formulario,
                'analise' => $analise,
                'cabecalhoPadrao' => $cabecalhoPadrao,
            ])->render();

            $browsershot = Browsershot::html($html)
                ->newHeadless()
                ->noSandbox()
                ->waitUntilNetworkIdle()
                ->showBackground()
                ->format(self::PDF_FORMAT)
                ->margins(
                    self::PDF_TOP_MARGIN,
                    self::PDF_RIGHT_MARGIN,
                    self::PDF_BOTTOM_MARGIN,
                    self::PDF_LEFT_MARGIN
                )
                ->setOption('addStyleTag', json_encode(['path' => $cssRenderPath]))
                ->setOption('protocolTimeout', 120 * 1000)
                ->setOption('args', [
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process',
                    '--disable-site-isolation-trials',
                    '--allow-file-access-from-files',
                ])
                ->setScreenshotType('jpeg', 100)
                ->timeout(120);

            if ($formulario->orientacao === 'landscape') {
                $browsershot->landscape()->emulateMedia(self::PDF_MEDIA_LANDSCAPE);
            } else {
                $browsershot->emulateMedia(self::PDF_MEDIA_PORTRAIT);
            }

            $pdfBinary = $browsershot->pdf();

            $stream = fopen('php://temp', 'r+');
            fwrite($stream, $pdfBinary);
            rewind($stream);

            $fpdiForCount = new Fpdi;
            $pageCount = $fpdiForCount->setSourceFile($stream);

            $formsData[] = [
                'nome' => $formulario->nome,
                'stream' => $stream,
                'page_count' => $pageCount,
                'capa' => (bool) $formulario->capa,
                'html' => $html,
            ];
        }

        return $formsData;
    }

    private function mergeStreamsSemSumario(array $coverItem, array $nonCoverFormItems): string
    {
        $temporaryDir = sys_get_temp_dir();
        $tempPath = tempnam($temporaryDir, 'analise_merge_').'.pdf';
        $pdfNoSumario = new Fpdi;

        $streams = array_merge(
            // [['stream' => $coverItem['stream']]],
            $nonCoverFormItems
        );

        foreach ($streams as $source) {
            $srcStream = $source['stream'];
            $pages = $pdfNoSumario->setSourceFile($srcStream);

            for ($i = 1; $i <= $pages; $i++) {
                $tplId = $pdfNoSumario->importPage($i);
                $tplSize = $pdfNoSumario->getTemplateSize($tplId);
                $orientation = ($tplSize['width'] > $tplSize['height']) ? 'L' : 'P';

                $pdfNoSumario->AddPage($orientation, [$tplSize['width'], $tplSize['height']]);
                $pdfNoSumario->useTemplate($tplId);
            }
        }

        $pdfNoSumario->Output('F', $tempPath);

        return $tempPath;
    }

    private function getSumarioItems(array $nonCoverFormItems, array $pdfPages, array $startPages): array
    {
        $sumarioItems = [];
        $normalizeForSearch = function (string $str): string {
            $str = preg_replace('/-\s*\n/u', '', $str);
            $str = preg_replace('/\x{00A0}+/u', ' ', $str);
            $str = preg_replace('/\s+/u', ' ', $str);
            $str = trim($str);
            $str = mb_strtolower($str, 'UTF-8');
            $str = str_replace(' ', '', $str);

            return $str;
        };

        foreach ($nonCoverFormItems as $i => $formInfo) {
            $headings = $this->extractHeadings($formInfo['html']);
            $formStartPage = $startPages[$i];
            $formPageCount = $formInfo['page_count'];
            $pageIndexStart = $formStartPage - 1;
            $pageIndexEnd = $pageIndexStart + $formPageCount - 1;

            foreach ($headings as $heading) {
                $tituloOriginal = $heading['title'];
                $tituloParaBusca = $normalizeForSearch($tituloOriginal);
                $paginaEncontrada = null;

                for ($p = $pageIndexStart; $p <= $pageIndexEnd; $p++) {
                    $textoPagina = $pdfPages[$p]->getText();
                    $textoParaBusca = $normalizeForSearch($textoPagina);

                    if (mb_stripos($textoParaBusca, $tituloParaBusca, 0, 'UTF-8') !== false) {
                        $paginaEncontrada = $p + 2;
                        break;
                    }
                }

                if ($paginaEncontrada !== null) {
                    $sumarioItems[] = [
                        'level' => (int) $heading['level'],
                        'title' => $tituloOriginal,
                        'page' => $paginaEncontrada,
                    ];
                }
            }
        }

        return $sumarioItems;
    }

    private function generateSumarioStream(array $sumarioItems)
    {
        $sumarioOrientation = 'P';
        $sumarioFormat = 'A4';
        $sumarioTopMargin = 30;
        $sumarioLeftMargin = 20;
        $sumarioRightMargin = 20;
        $sumarioBottomMargin = 20;
        $sumarioTitleFontSize = 20;
        $sumarioItemFontSize = 12;
        $sumarioLineHeight = 8;
        $indentLevel2Mm = 10;

        $pages = array_column($sumarioItems, 'page');
        array_multisort($pages, SORT_ASC, $sumarioItems);

        $pageHeight = 297; // Altura da página A4 em mm
        $usableHeight = $pageHeight - $sumarioTopMargin - $sumarioBottomMargin;
        $linesPerPage = floor($usableHeight / $sumarioLineHeight);

        $totalItems = count($sumarioItems);
        $sumarioPageCount = ceil($totalItems / $linesPerPage) - 1;

        foreach ($sumarioItems as &$item) {
            $item['page'] += $sumarioPageCount;
        }
        unset($item);

        $pdf = new Fpdi;
        $pdf->AddPage($sumarioOrientation, $sumarioFormat);
        $pdf->SetAutoPageBreak(true, $sumarioBottomMargin);
        $pdf->SetFont('Times', 'B', $sumarioTitleFontSize);
        $pdf->SetXY($sumarioLeftMargin, $sumarioTopMargin);
        $pdf->Cell(0, 10, utf8_decode('Sumário'), 0, 1, 'L');

        // Loop de itens
        foreach ($sumarioItems as $item) {
            $nivel = (int) $item['level'];
            $textoTitulo = $item['title'];
            $numeroPagina = (string) $item['page'];

            $xOffset = ($nivel === 2) ? $indentLevel2Mm : 0;
            $xPos = $sumarioLeftMargin + $xOffset;

            $pdf->SetFont('Times', $nivel === 1 ? 'B' : '', $sumarioItemFontSize);

            $titleWidth = $pdf->GetStringWidth($textoTitulo);
            $pageNumWidth = $pdf->GetStringWidth($numeroPagina);
            $dotWidth = $pdf->GetStringWidth('.');

            $usableWidth = $pdf->GetPageWidth() - $sumarioRightMargin - $xPos;

            $remaining = $usableWidth - $titleWidth - $pageNumWidth - 1;
            $dotsCount = ($dotWidth > 0 && $remaining > 0) ? floor($remaining / $dotWidth) : 0;

            $dots = str_repeat('.', $dotsCount);
            $lineWithDots = $textoTitulo.' '.$dots;

            $pdf->SetX($xPos);
            $pdf->Cell($usableWidth, $sumarioLineHeight, utf8_decode($lineWithDots), 0, 0, 'L');
            $pdf->Cell($pageNumWidth, $sumarioLineHeight, $numeroPagina, 0, 1, 'R');
        }

        $stream = fopen('php://temp', 'r+');
        $binary = $pdf->Output('S');
        fwrite($stream, $binary);
        rewind($stream);

        return $stream;
    }

    public function exportAnalisePDF(Analise $analise)
    {
        // try {
            $formsData = $this->buildFormsData($analise);
            $nonCoverFormItems = collect($formsData)
                ->reject(fn ($f) => $f['capa'] === true)
                ->values()
                ->all();

            $coverItem = collect($formsData)->firstWhere('capa', true);
            $coverPageCount = $coverItem['page_count'] ?? 0;
            $offset = $coverPageCount;

            $startPages = [];
            foreach ($nonCoverFormItems as $i => $form) {
                $startPages[$i] = $offset + 1;
                $offset += $form['page_count'];
            }

            $parser = new Parser;

            $tempMergedPath = $this->mergeStreamsSemSumario($coverItem, $nonCoverFormItems);
            $pdfPages = $parser->parseFile($tempMergedPath)->getPages();
            $sumarioItems = $this->getSumarioItems($nonCoverFormItems, $pdfPages, $startPages);
            $sumarioStream = $this->generateSumarioStream($sumarioItems);
            $sumarioPageCount = ((new Fpdi)->setSourceFile($sumarioStream)) + 1;

            $streamsSequence = array_merge(
                // [['stream' => $coverItem['stream']]],
                [['stream' => $sumarioStream]],
                $nonCoverFormItems
            );

            $pdfAssembler = new Fpdi;
            foreach ($streamsSequence as $source) {
                $srcStream = $source['stream'];
                $pagesCount = $pdfAssembler->setSourceFile($srcStream);

                for ($p = 1; $p <= $pagesCount; $p++) {
                    $tplId = $pdfAssembler->importPage($p);
                    $tplSize = $pdfAssembler->getTemplateSize($tplId);
                    $orientation = ($tplSize['width'] > $tplSize['height']) ? 'L' : 'P';

                    $pdfAssembler->AddPage($orientation, [$tplSize['width'], $tplSize['height']]);
                    $currentPage = $pdfAssembler->PageNo();

                    if ($currentPage >= ($sumarioPageCount + $coverPageCount)) {
                        $pdfAssembler->SetFont('Times', '', 10);
                        $pdfAssembler->SetY(-16);
                        $pdfAssembler->Cell(0, -5, (string) $currentPage, 0, 0, 'R');
                    }

                    $pdfAssembler->useTemplate($tplId);
                }
            }

            $finalPdfPath = storage_path('app/pdf_analise_'.now()->format('YmdHis').'.pdf');
            $pdfAssembler->Output('F', $finalPdfPath);

            @unlink($tempMergedPath);
            register_shutdown_function(fn () => @unlink($finalPdfPath));

            return $finalPdfPath;
        /*} catch (\Throwable $e) {
            \Log::error('Erro no Trait ao gerar o PDF de formulários da análise: '.$e->getMessage());

            return null;
        }*/
    }
}
