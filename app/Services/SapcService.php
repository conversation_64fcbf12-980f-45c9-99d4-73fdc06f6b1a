<?php

namespace App\Services;

use App\Enums\Esfera;
use App\Enums\NaturezaValorEnum;
use App\Enums\Sapc\FormularioHistoricoAcao;
use App\Enums\Sapc\StatusAnalise;
use App\Enums\Sapc\StatusAnaliseFormulario;
use App\Enums\TipoLayout;
use App\Models\Sapc\Analise;
use App\Models\Sapc\AnaliseFormulario;
use App\Models\Sapc\AnaliseFormularioAnexo;
use App\Models\Sapc\AnaliseFormularioHistoricoAcao;
use App\Models\Sapc\AnaliseFormularioVersao;
use App\Models\Sapc\ModeloAnalise;
use App\Models\Sapc\ModeloAnaliseFormulario;
use App\Models\Sapc\ModeloFormulario;
use App\Models\Sapc\Parametrizacao;
use App\Models\Sapc\VariavelContaGoverno;
use App\Models\UnidadeGestora;
use App\Traits\GenerateAnalisePDFTrait;
use App\Traits\SapcMatriz;
use App\Traits\SapcParseText;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SapcService
{
    use GenerateAnalisePDFTrait;

    const PDF_TOP_MARGIN = 0;

    const PDF_RIGHT_MARGIN = 0;

    const PDF_BOTTOM_MARGIN = 0;

    const PDF_LEFT_MARGIN = 0;

    const PDF_FORMAT = 'A4';

    const PDF_MEDIA = 'print';

    const VAR_NOT_FOUND = '|variavel_nao_encontrada|';

    use SapcMatriz;
    use SapcParseText;

    private $variaveis = [];

    private $listaVariaveisGoverno;

    public function __construct()
    {
        $this->listaVariaveisGoverno = VariavelContaGoverno::get();
    }

    /**
     * Cria análises para cada Unidade Gestora (UG) fornecida.
     *
     * @param  array|int  $ugs  IDs ou ID da UG
     * @param  int  $exercicio  Ano fiscal da análise
     * @param  array  $data  Dados adicionais para análise
     *
     * @throws \Exception Se os parâmetros obrigatórios não forem fornecidos
     */
    public function criarAnalisesPorUG($unidadeGestora, $exercicio)
    {
        $modeloAnalise = $unidadeGestora->tipoUnidade->modelosAnalises()->whereIn('esfera', [$unidadeGestora->esfera->value, Esfera::EstadualEMunicipal])->first();

        if (is_null($modeloAnalise)) {
            \Log::error('Não foi possível encontrar um modelo de análise para a UG '.$unidadeGestora->id);

            return null;
        }

        $data['modelo_analise_id'] = $modeloAnalise->id;
        $data['exercicio'] = $exercicio;
        $data['unidade_gestora_id'] = $unidadeGestora->id;
        $data['diretoria_id'] = $modeloAnalise->diretoria_id;

        $analise = $this->criarAnalise($data);

        return $analise;
    }

    public function gerarAnalisesPorModelo($ugs, $exercicio, $modelo_analise_id = null)
    {
        $analises = [];

        if (! is_null($modelo_analise_id)) {
            foreach ($ugs as $ug) {
                $modeloAnalise = ModeloAnalise::with(['modelosAnalisesFormularios' => function ($query) {
                    $query->where(function ($subQuery) {
                        $subQuery->where('data_fim', '>=', today())->orWhereNull('data_fim');
                    });
                }])
                    ->where('id', $modelo_analise_id)
                    ->whereIn('esfera', [$ug->esfera->value, Esfera::EstadualEMunicipal])
                    ->where(function ($query) {
                        $query
                            ->where('data_inicio', '<=', today())
                            ->where(function ($subQuery) {
                                $subQuery->where('data_fim', '>=', today())->orWhereNull('data_fim');
                            });
                    })
                    ->first();

                if (! is_null($modeloAnalise)) {
                    $data = [
                        'unidade_gestora_id' => $ug->id,
                        'exercicio' => $exercicio,
                        'modelo_analise_id' => $modeloAnalise->id,
                        'diretoria_id' => $modeloAnalise->diretoria_id,
                    ];

                    $analises[] = $this->criarAnalise($data);
                }
            }
        } else {
            foreach ($ugs as $ug) {
                $unidadeGestora = UnidadeGestora::with('tipoUnidade.modelosAnalises')
                    ->where('id', $ug)
                    ->first();

                $analises[] = self::criarAnalisesPorUG($unidadeGestora, $exercicio);
            }
        }

        return $analises;
    }

    /**
     * Cria uma única análise com base nos dados fornecidos.
     *
     * @param  array  $data  Dados da análise
     *
     * @throws \Exception Se os parâmetros obrigatórios não forem fornecidos
     */
    public function criarAnalise($data)
    {
        if (empty($data['unidade_gestora_id'])) {
            throw new Exception('parametro UG e obrigatorio', 400);
        }

        $data['status'] = StatusAnalise::AguardandoInicio;
        $data['data_criacao'] = Carbon::now();
        $data['data_conclusao'] = null;
        $data['assinatura'] = null;

        $analise = Analise::create($data);

        if ($analise && $this->temRemessaFechamento($analise)) {
            $analise->status = 'aguardando_remessa';
            $analise->save();
        }

        $unidadeGestoraIds = $analise->unidadeGestora->unidadesGestorasSubsidiarias->pluck('id')->toArray();
        $unidadeGestoraIds[] = $analise->unidade_gestora_id;

        foreach ($unidadeGestoraIds as $unidadeGestoraId) {
            Log::info('UG: '.$unidadeGestoraId);
            Log::info('Exercicio: '.$analise->exercicio);
            $matriz = $this->buscarMatriz($unidadeGestoraId, $analise->exercicio);
            Log::info('Exercicio Anterior: '.$analise->exercicio - 1);
            $matrizAnterior = $this->buscarMatriz($unidadeGestoraId, $analise->exercicio - 1);

            foreach ($this->listaVariaveisGoverno as $objVarGoverno) {
                $total = $this->calcularVariavel($objVarGoverno, $matriz);
                $totalAnterior = $this->calcularVariavel($objVarGoverno, $matrizAnterior);

                $this->variaveis[$objVarGoverno->nome] = ($this->variaveis[$objVarGoverno->nome] ?? 0) + $total;
                $this->variaveis[$objVarGoverno->nome.'__1'] = ($this->variaveis[$objVarGoverno->nome.'__1'] ?? 0) + $totalAnterior;
            }
        }

        $this->criarAnalisesFormularios($analise);

        return $analise;
    }

    /**
     * Verifica se existe s remessa de fechamento na $analise
     */
    public function temRemessaFechamento(Analise $analise)
    {
        $remessas = $this->getRemessasDaAnalise($analise);

        return $remessas->map(function ($remessa) {
            return ($remessa->periodoRemessa->bimestre->value == Parametrizacao::getValor('gerar_analise_bimestre')) ? true : false;
        })->contains(true);
    }

    /**
     * Verifica se existe remessas com inadimplencia na $analise
     */
    public function temRemessaInadimplente(Analise $analise)
    {
        $remessas = $this->getRemessasDaAnalise($analise);

        return $remessas->map(function ($remessa) {
            return $remessa->isInadimplente();
        })->contains(true);
    }

    /**
     * Cria análises de formulários em lote associadas a uma análise principal.
     *
     * @param  Analise  $analise  Análise principal
     * @param  $modeloAnalise  Modelo de análise
     * @param  array  $data  Dados adicionais para criação
     * @return bool Sucesso da operação
     */
    public function criarAnalisesFormularios(Analise $analise)
    {
        $modeloAnalise = $analise->modeloAnalise;

        $dadosFormularios = [];

        foreach ($modeloAnalise->modelosAnalisesFormularios as $modelosAnalisesFormulario) {
            if (! is_null($modelosAnalisesFormulario->data_fim) && $modelosAnalisesFormulario->data_fim < today()) {
                continue;
            }
            $dadosFormularios[] = $this->prepararDadosFormulario($analise, $modelosAnalisesFormulario);
        }

        if (! empty($dadosFormularios)) {
            AnaliseFormulario::insert($dadosFormularios);
        }

        return true;
    }

    /**
     * Prepara os dados de um formulário para inserção em lote.
     *
     * @param  Analise  $analise  Análise principal
     * @param  ModeloAnaliseFormulario  $modelosAnalisesFormulario  Modelo de formulário
     * @return array Dados preparados para inserção
     */
    protected function prepararDadosFormulario(Analise $analise, ModeloAnaliseFormulario $modelosAnalisesFormulario)
    {
        Log::info('Preparando dados do formulário: '.$modelosAnalisesFormulario->modeloFormulario->id);

        return [
            'modelo_formulario_id' => $modelosAnalisesFormulario->modeloFormulario->id,
            'analise_id' => $analise->id,
            'data_criacao' => Carbon::now(),
            'data_preenchimento' => null,
            'texto' => $this->parseTexto($analise, $modelosAnalisesFormulario->modeloFormulario->texto),
            'versao' => 1,
            'user_id' => Auth::id(),
            'status' => StatusAnaliseFormulario::AguardandoInicio,
        ];
    }

    public function downloadFormulariosAnalise(Analise $analise)
    {
        try {
            $analise = $analise->load([
                'unidadeGestora',
                'diretoria',
                'diretoria.users',
                'analisesFormularios' => function ($query) {
                    $query->join('sapc.modelo_formulario', 'sapc.analise_formulario.modelo_formulario_id', '=', 'sapc.modelo_formulario.id')
                        ->join('sapc.modelo_analise_formulario', 'sapc.modelo_formulario.id', '=', 'sapc.modelo_analise_formulario.modelo_formulario_id')
                        ->orderBy('sapc.modelo_analise_formulario.ordenacao', 'ASC')
                        ->select([
                            'sapc.analise_formulario.*',
                            'sapc.modelo_formulario.orientacao',
                            'sapc.modelo_formulario.nome',
                            'sapc.modelo_formulario.capa',
                        ]);
                },
            ]);

            $pdfName = "formularios_analise_{$analise->id}.pdf";
            $pdfPath = $this->exportAnalisePDF($analise);

            return [
                'name' => $pdfName,
                'pdf_path' => $pdfPath,
            ];
        } catch (\Throwable $e) {
            Log::error('Erro ao gerar o PDF de formulários da análise: '.$e->getMessage());

            return null;
        }
    }

    public function calcularVariavel($variavelContaGoverno, $matriz)
    {
        bcscale(2);
        $total = 0;

        if (! isset($matriz[$variavelContaGoverno->id])) {
            return $total;
        }

        foreach ($matriz[$variavelContaGoverno->id] as $row) {
            $periodoInicial = $variavelContaGoverno->periodo_inicial;
            $periodoFinal = $variavelContaGoverno->periodo_final;

            if ($row->bimestre >= $periodoInicial && $row->bimestre <= $periodoFinal) {

                $moneyValorConta = (string) $row->valor;
                $classe = substr($row->conta, 0, 1);

                if (in_array($classe, [1, 3, 5, 7])) {
                    if ($row->natureza_valor == NaturezaValorEnum::CREDITO) {
                        $total = bcsub($total, $moneyValorConta);
                    } else {
                        $total = bcadd($total, $moneyValorConta);
                    }
                }

                if (in_array($classe, [2, 4, 6, 8])) {
                    if ($row->natureza_valor == NaturezaValorEnum::DEBITO) {
                        $total = bcsub($total, $moneyValorConta);
                    } else {
                        $total = bcadd($total, $moneyValorConta);
                    }
                }
            }
        }

        return $total;
    }

    public function uploadAnaliseFormularioAnexo(AnaliseFormulario $analiseFormulario, $file, $descricao = '', $ordem = 1)
    {
        try {
            $user_id = Auth::id();

            $uploadPath = storage_path('app/analises/formularios/anexos');
            $fileName = $file->getClientOriginalName();

            $file->move($uploadPath, $fileName);

            $anexo = new AnaliseFormularioAnexo;
            $anexo->analise_formulario_id = $analiseFormulario->id;
            $anexo->arquivo = $fileName;
            $anexo->descricao = $descricao;
            $anexo->ordem = $ordem;
            $anexo->user_id = $user_id;
            $anexo->save();

            return ['success' => true, 'message' => 'Arquivo enviado com sucesso'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Erro ao processar o arquivo', 'error' => $e->getMessage()];
        }
    }

    public function parseTexto(Analise $analise, $texto)
    {
        $parsedTexto = htmlspecialchars_decode($texto);

        Log::info('INI VAR GOVERNO: ');
        $parsedTexto = $this->replaceVariaveisGoverno($parsedTexto, $analise, $this->listaVariaveisGoverno->keyBy('nome'));

        Log::info('INI VAR GESTAO: ');
        $parsedTexto = $this->replaceVariaveisGestao($parsedTexto, $analise);

        Log::info('INI VAR ANALISE: ');
        $parsedTexto = $this->replaceVariaveisAnalise($parsedTexto, $analise);

        Log::info('INI VAR LEI ORCAMENTARIA: ');
        $parsedTexto = $this->replaceVariaveisLeisOrcamentarias($parsedTexto, $analise);

        Log::info('INI FORMULAS: ');
        $parsedTexto = $this->replaceFormulas($parsedTexto, $analise);

        Log::info('INI VAR LISTA: ');
        $parsedTexto = $this->replaceVariaveisLista($parsedTexto, $analise);

        Log::info('INI REFERENCIAS: ');
        $parsedTexto = $this->replaceReferencias($parsedTexto, $analise);

        return $parsedTexto;
    }

    /**
     * Substitui marcadores de referências no texto
     */
    private function replaceReferencias(string $texto, Analise $analise): string
    {
        // Verificar se o texto contém o marcador de referências
        if (!str_contains($texto, '$_marcador{Referencia}')) {
            return $texto;
        }

        try {
            // Extrair referências da análise
            $referencias = $this->extrairReferencias($analise);

            if (empty($referencias)) {
                // Se não há referências, remover o marcador
                return str_replace('$_marcador{Referencia}', '', $texto);
            }

            // Gerar HTML da lista de referências
            $listaHtml = view('pdf.sapc.analise.referencias', ['referencias' => $referencias])->render();

            // Substituir o marcador pela lista
            $textoProcessado = str_replace('$_marcador{Referencia}', $listaHtml, $texto);

            Log::info('Marcador de referências processado. Total de referências: ' . count($referencias));

            return $textoProcessado;

        } catch (\Exception $e) {
            Log::warning('Erro ao processar marcador de referências: ' . $e->getMessage());
            // Em caso de erro, retornar o texto original
            return $texto;
        }
    }

    public function listarAnexos(AnaliseFormulario $analiseFormulario)
    {
        return $analiseFormulario
            ->analisesFormulariosAnexos()
            ->paginate(request()->input('itemsPerPage', 20));
    }

    public function excluirAnaliseFormularioAnexo(AnaliseFormularioAnexo $anexo)
    {
        try {
            Storage::delete('app/analises/formularios/anexos/'.$anexo->arquivo);
            $anexo->delete();

            return ['success' => true, 'message' => 'Anexo excluido com sucesso'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Erro ao excluir o anexo', 'error' => $e->getMessage()];
        }
    }

    public function restaurarVersao(AnaliseFormularioVersao $versaoARestaurar)
    {
        try {
            $versaoAtual = AnaliseFormulario::query()
                ->find($versaoARestaurar->analise_formulario_id);

            AnaliseFormularioVersao::query()->create([
                'versao' => $versaoAtual->versao,
                'analise_formulario_id' => $versaoAtual->id,
                'data_preenchimento' => $versaoAtual->data_preenchimento,
                'texto' => $versaoAtual->texto,
                'user_id' => $versaoAtual->user_id,
            ]);

            $versaoAtual->fill([
                'user_id' => $versaoARestaurar->user_id,
                'versao' => ++$versaoAtual->versao,
                'data_preenchimento' => $versaoARestaurar->data_preenchimento,
                'texto' => $versaoARestaurar->texto,
            ])->saveQuietly();

            return $versaoAtual;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function listarVersoes(AnaliseFormulario $analiseFormulario)
    {
        return $analiseFormulario
            ->analisesFormulariosVersao()
            ->paginate(request()->input('itemsPerPage', 20));
    }

    public function logAcao(AnaliseFormulario $formulario, string $acao, string $valor)
    {
        return AnaliseFormularioHistoricoAcao::create([
            'analise_formulario_id' => $formulario->id,
            'acao' => $acao,
            'valor' => $valor,
            'user_id' => auth()->user()->id,
            'data_acao' => now(),
        ]);
    }

    public function getAcoes(AnaliseFormulario $formulario)
    {
        return AnaliseFormularioHistoricoAcao::query()
            ->with('usuario')
            ->where('analise_formulario_id', $formulario->id)
            ->orderBy('id', 'desc')
            ->paginate(request()->input('itemsPerPage'));
    }

    public function getRemessasParciasDaAnalise(Analise $analise)
    {
        $exercicio = $analise->exercicio;
        $remessas = $analise->unidadeGestora->remessas()
            ->whereHas('periodoRemessa', function ($query) use ($exercicio) {
                $query->where('exercicio', $exercicio);
                $query->where('permanente', false);
            })
            ->whereHas('parciais')->with(['parciais' => function ($query) {
                $query->whereIn('tipo', [
                    TipoLayout::ExecucaoOrcamentariaFinanceiraPatrimonialContabil,
                    TipoLayout::EncerramentoExercicio,
                ]);
            }])
            ->get();

        $parciais = $remessas->flatMap(function ($remessa) {
            return $remessa->parciais;
        });

        return $parciais;
    }

    public function getRemessasDaAnalise(Analise $analise)
    {
        $exercicio = $analise->exercicio;
        $remessas = $analise->unidadeGestora->remessas()
            ->whereHas('periodoRemessa', function ($query) use ($exercicio) {
                $query->where('exercicio', $exercicio);
                $query->where('permanente', false);
            })
            ->get();

        return $remessas;
    }

    public function storeAnalise(AnaliseFormulario $analiseFormulario, array $data)
    {
        // GERAÇÃO DE NOVA VERSAO
        AnaliseFormularioVersao::create([
            'versao' => $analiseFormulario->versao,
            'texto' => $analiseFormulario->texto,
            'data_preenchimento' => (($analiseFormulario->data_preenchimento) ? $analiseFormulario->data_preenchimento : now()),
            'analise_formulario_id' => $analiseFormulario->id,
            'user_id' => $analiseFormulario->user_id,
        ]);
        $version = ($analiseFormulario->versao ?? 0) + 1;
        $analiseFormulario->versao = $version;

        $analiseFormulario->status = $analiseFormulario->status == StatusAnaliseFormulario::Finalizado ? $analiseFormulario->status : $data['status'];
        $analiseFormulario->texto = $data['texto'];
        $analiseFormulario->data_preenchimento = now();
        $analiseFormulario->user_id = Auth::id();
        $analiseFormulario->texto_auto_save = null;
        $analiseFormulario->save();

        if ($analiseFormulario->status === StatusAnaliseFormulario::Finalizado) {
            $this->logAcao($analiseFormulario, FormularioHistoricoAcao::FinalizarFormulario, $analiseFormulario->versao);
        } else {
            $this->logAcao($analiseFormulario, FormularioHistoricoAcao::SalvarFormulario, $analiseFormulario->versao);
        }

        return $analiseFormulario;
    }

    public function autoSaveAnalise($analiseId, $analiseFormularioId, $request)
    {
        if (! Analise::whereId($analiseId)->exists()) {
            throw new Exception('Análise Não encontrada nos registros.');
        }

        $analiseFormulario = AnaliseFormulario::find($analiseFormularioId);

        if (! $analiseFormulario) {
            throw new Exception('Formulário de Análise Não encontrada nos registros.');
        }

        $analiseFormulario
            ->update(['texto_auto_save' => $request->input('text')]);
    }

    public function updateProtocoloEtce($analiseId, $protocolo_etce)
    {
        $analise = Analise::findOrFail($analiseId);
        $analise->protocolo_etce = $protocolo_etce;
        $analise->save();
    }

    /**
     * Gera as referências para o formulário de referências da análise
     */
    public function gerarReferencias(Analise $analise, string $marcador = '$_marcador{Referencia}')
    {
        try {
            $referencias = $this->extrairReferencias($analise);
            if (empty($referencias)) {
                return false;
            }

            $formularioReferencias = $analise->analisesFormularios()
                ->whereRelation('modeloFormulario', 'referencias', true)
                ->first();

            if (! $formularioReferencias) {
                $modeloReferencias = ModeloFormulario::where('referencias', true)->first();
                $formularioReferencias = $analise->analisesFormularios()
                    ->create([
                        'modelo_formulario_id' => $modeloReferencias?->id,
                        'user_id' => Auth::id(),
                        'data_criacao' => now(),
                        'texto' => $modeloReferencias?->texto,
                        'versao' => 1,
                    ]);
            }

            $listaHtml = view('pdf.sapc.analise.referencias', compact('referencias'))->render();
            $formularioReferencias->texto = str_replace(
                $marcador, $listaHtml, $formularioReferencias->texto
            );
            $formularioReferencias->save();

            Log::info('Referências geradas com sucesso para a análise: '.$analise->id.'. Total de referências: '.count($referencias));

            return true;
        } catch (\Exception $e) {
            Log::error('Erro ao gerar referências para a análise '.$analise->id.': '.$e->getMessage());

            return false;
        }
    }
}
