<template>
  <v-app>
    <transition name="fade-in-up">
      <div v-show="isComponentMounted">
        <KTCard title="">
          <template v-slot:body>
            <div class="text-center py-3" v-if="!analise">
              <v-row>
                <v-col cols="12">
                  <v-skeleton-loader type="heading"> </v-skeleton-loader>
                </v-col>
                <v-col cols="6">
                  <v-skeleton-loader
                    type="list-item, divider, list-item, divider, list-item, divider, list-item, divider, actions"
                  >
                  </v-skeleton-loader>
                </v-col>
                <v-col cols="6">
                  <SkeletonTable class="div-table" />
                </v-col>
              </v-row>
            </div>

            <template v-else>
              <div class="mx-auto">
                <v-card-actions id="top-actions" class="pt-0">
                  <h2 class="mr-auto capitalize">
                    Análise: {{ analise.modelo.nome }}
                  </h2>
                </v-card-actions>
              </div>

              <div>
                <v-card width="100%">
                  <v-row>
                    <v-col cols="6">
                      <v-form ref="form" lazy-validation>
                        <v-card-text>
                          <v-container>
                            <v-row no-gutters class="p-2">
                              <v-col class="mx-1">
                                <v-text-field
                                  v-model="analise.id"
                                  label="Número"
                                  type="number"
                                  readonly
                                  density="comfortable"
                                ></v-text-field>
                              </v-col>

                              <v-col class="mx-1">
                                <v-text-field
                                  v-model="analise.exercicio"
                                  label="Exercício"
                                  readonly
                                  density="comfortable"
                                ></v-text-field>
                              </v-col>
                            </v-row>

                            <v-row no-gutters class="p-2">
                              <v-col class="mx-1">
                                <v-text-field
                                  v-model="analise.protocolo_etce"
                                  label="Protocolo eTCE"
                                  density="comfortable"
                                  maxlength="16"
                                  counter="16"
                                  @blur="salvarProtocoloETCE"
                                ></v-text-field>
                              </v-col>
                            </v-row>

                            <v-row no-gutters class="p-2">
                              <v-col class="mx-1">
                                <v-text-field
                                  v-model="
                                    analise.unidade_gestora.nome_com_cidade
                                  "
                                  label="Unidade Gestora"
                                  readonly
                                  density="comfortable"
                                ></v-text-field>
                              </v-col>
                            </v-row>

                            <v-row
                              v-if="analise.diretoria && analise.diretoria.nome"
                              no-gutters
                              class="p-2"
                            >
                              <v-col class="mx-1">
                                <v-textarea
                                  v-model="analise.diretoria.nome"
                                  density="comfortable"
                                  label="Diretoria"
                                  readonly
                                  auto-grow
                                  rows="1"
                                ></v-textarea>
                              </v-col>
                            </v-row>

                            <v-row
                              v-if="
                                analise.responsavel && analise.responsavel.name
                              "
                              no-gutters
                              class="p-2"
                            >
                              <v-col class="mx-1">
                                <v-text-field
                                  v-model="analise.responsavel.name"
                                  label="Diretor Responsável"
                                  density="comfortable"
                                  readonly
                                ></v-text-field>
                              </v-col>
                            </v-row>
                          </v-container>
                        </v-card-text>

                        <!-- action buttons -->
                        <div class="text-end px-4 mb-2">
                          <v-btn
                            color="primary"
                            class="mr-2"
                            @click="start"
                            v-show="analise.status === 'aguardando_inicio'"
                            :disabled="
                              loading ||
                              loadingForms ||
                              !hasRemessas ||
                              hasSomeRemessaInadimplente
                            "
                          >
                            Iniciar
                            <i v-if="loading" class="v-spinner"></i>
                          </v-btn>
                          <v-btn
                            color="secondary"
                            class="mr-2 d-inline-flex"
                            small
                            @click="atualizarMarcadores"
                            :disabled="
                              loading ||
                              loadingForms ||
                              !hasRemessas ||
                              hasSomeRemessaInadimplente ||
                              atualizandoReferencias
                            "
                            v-show="analise.status !== 'aguardando_inicio'"
                          >
                            <AiIcon>
                              <IconSolarRefreshLinear
                                width="22px"
                                height="22px"
                              />
                            </AiIcon>
                            <span class="ml-1">Atualizar Marcadores</span>
                            <i v-if="atualizandoReferencias" class="v-spinner"></i>
                          </v-btn>
                          <v-btn
                            color="primary"
                            class="mr-2 d-inline-flex"
                            small
                            @click="printAnalise(analise.id)"
                            :disabled="
                              loading ||
                              loadingForms ||
                              !hasRemessas ||
                              hasSomeRemessaInadimplente ||
                              isDownloading
                            "
                          >
                            <AiIcon>
                              <IconSolarPrinter2Linear
                                width="22px"
                                height="22px"
                              />
                            </AiIcon>
                            <span class="ml-1">Imprimir</span>
                            <i v-if="loading" class="v-spinner"></i>
                          </v-btn>
                        </div>
                      </v-form>
                    </v-col>

                    <!-- remessas -->
                    <v-col cols="6">
                      <strong class="ml-1">Remessas do Exercício:</strong>

                      <v-row no-gutters class="p-1">
                        <v-col v-if="remessas">
                          <SkeletonTable
                            class="div-table"
                            v-show="loadingTable"
                          />
                          <v-data-table
                            id="table-remessas-exercicio"
                            v-show="!loadingTable"
                            :headers="headers"
                            :items="remessas"
                            :items-per-page="-1"
                            v-model:sort-by="sortBy"
                            no-data-text="Não foram encontradas remessas nesta consulta"
                            class="custom-table-managingUnit elevation-2 mb-2"
                          >
                            <!-- Centralizando header separado do body -->
                            <template v-slot:headers>
                              <tr>
                                <th
                                  v-for="header in headers"
                                  :key="header.value"
                                  class="text-center"
                                >
                                  {{ header.title }}
                                </th>
                              </tr>
                            </template>

                            <template v-slot:[`item.nome`]="{ item }">
                              <a
                                :href="
                                  './admin/resources/remessa-parcials/' +
                                  item.id
                                "
                                target="_blank"
                                >{{ item.nome }}</a
                              >
                            </template>

                            <template v-slot:[`item.periodo`]="{ item }">
                              {{ item.periodo_descricao }}
                            </template>

                            <template v-slot:[`item.situacao`]="{ item }">
                              <AiIcon
                                v-if="
                                  item.status === 'waiting-file' ||
                                  item.status === 'user-canceled'
                                "
                                color="text-red-darken-1"
                              >
                                <IconSolarCloseSquareBold
                                  width="22px"
                                  height="22px"
                                />
                              </AiIcon>
                              <AiIcon
                                v-else-if="item.status === 'signed'"
                                color="text-green"
                              >
                                <IconSolarCheckReadLinear
                                  width="22px"
                                  height="22px"
                                />
                              </AiIcon>
                              <div class="ml-1">{{ item.situacao }}</div>
                            </template>

                            <!-- Desabilitando Footer -->
                            <template v-slot:bottom></template>
                          </v-data-table>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card>
              </div>
            </template>
          </template>
        </KTCard>
      </div>
    </transition>
  </v-app>
</template>

<script>
  import KTCard from "@/view-metronic-auditor/content/Card.vue";
  import Toaster from "@/components/Toaster.vue";
  import { VDataTable } from "vuetify/components/VDataTable";
  import SAPCApi from "@/api/auditor/sapc.js";
  import { defineComponent } from "vue";
  import { useAnaliseStore } from "@/stores/auditor/analiseStore";
  import SkeletonTable from "@/components/bradoc/SkeletonTable/SkeletonTable.vue";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useDownloadFile } from "@/composables/global/useDownloadFile.ts";
  import { useModalDownloadStore } from "@/stores/global/modalDownloadStore";

  const analiseStore = useAnaliseStore();

  export default defineComponent({
    name: "analiseInicio",
    components: {
      KTCard,
      VDataTable,
      SkeletonTable
    },
    props: {
      analiseId: {
        default: null
      }
    },
    data() {
      return {
        loading: true,
        loadingForms: false,
        loadingTable: false,
        atualizandoReferencias: false,
        firstFormId: null,
        unidades_gestoras: {},
        options: {
          exercicios: [],
          poderes: [],
          esferas: [],
          tiposUG: [],
          municipios: [],
          tiposAnalises: [],
          unidadesGestoras: []
        },
        sortBy: [{ key: "periodo", order: "asc" }],
        headers: [
          {
            title: "Remessa",
            value: "nome",
            align: "center",
            sortable: false
          },
          {
            title: "Período",
            value: "periodo",
            align: "center",
            sortable: true
          },
          {
            title: "Situação",
            value: "situacao",
            align: "center",
            sortable: true
          }
        ],
        isComponentMounted: false
      };
    },
    computed: {
      isDownloading() {
        return useModalDownloadStore().isDownloading;
      },
      analise() {
        return analiseStore.analise;
      },
      remessas() {
        return analiseStore.remessas;
      },
      hasSomeRemessaInadimplente() {
        return this.remessas
          ? this.remessas.some((remessa) => {
              return remessa.situacao_key === "inadimplente";
            })
          : false;
      },
      hasRemessas() {
        return this.remessas ? this.remessas.length > 0 : false;
      },
      hasAnalise() {
        return !!this.analise;
      }
    },
    mounted() {
      this.isComponentMounted = true;

      if (!this.analiseId) {
        this.backToAnalises();
        return;
      }

      this.fetchData(this.analiseId);
    },
    methods: {
      async salvarProtocoloETCE() {
        try {
          await analiseStore.updateProtocoloETCE(
            this.analise.id,
            this.analise.protocolo_etce
          );
        } catch (error) {
          this.handleError(error);
        }
      },
      async fetchData(analiseId) {
        useFetchStore().setFetchState("fetching");
        this.loading = true;
        this.loadingTable = true;

        try {
          await analiseStore.fetchAnalise(analiseId);
          await analiseStore.fetchRemessas(analiseId);
          useFetchStore().setFetchState("done");
        } catch (error) {
          useFetchStore().setFetchState("error");
          this.handleError(error);
          console.error(error);
        } finally {
          this.loading = false;
          this.loadingTable = false;
        }
      },
      backToAnalises() {
        this.$router.push("/e-contas/analises");
      },
      formatDate(value) {
        if (!value) return;
        const d = new Date(value);
        return d.toLocaleString("pt-BR").substr(0, 10);
      },

      async fetchRemessasExercicio(analiseId) {
        this.loadingTable = true;
        try {
          const remessas = await SAPCApi.fetchRemessasExercicio(analiseId);
          const remessasData = remessas.data.data;
          this.loadingTable = false;

          const remessasItens = [];
          remessasData.map((value) => {
            const remessaItem = {
              id: value.id,
              nome: value.periodo_remessa.nome,
              periodo: value.periodo_remessa.periodo,
              periodo_descricao: value.periodo_remessa.periodo_descricao,
              situacao: value.situacao
            };
            remessasItens.push(remessaItem);
          });
          this.remessas = remessasItens;
        } catch (error) {
          this.handleError(error);
        }
      },

      async fetchForms(analiseId) {
        this.loadingForms = true;
        try {
          const formularios = await SAPCApi.fetchFormularios(analiseId);
          this.loadingForms = false;
          this.firstFormId = formularios.data.data[0].id;
          return true;
        } catch (error) {
          this.loadingForms = false;
          console.error(error);
          return false;
        }
      },
      async start() {
        if (!this.hasRemessas || this.hasSomeRemessaInadimplente) return;

        await this.fetchForms(this.analiseId);
        const hasInadimplente = this.remessas.some(
          (remessa) => remessa.isInadimplente
        );
        if (this.firstFormId && !hasInadimplente) {
          this.$router.push(
            `/e-contas/analise/${this.analise.id}/formulario/${this.firstFormId}`
          );
        }
      },
      printAnalise(analiseId) {
        if (
          !this.hasRemessas ||
          this.hasSomeRemessaInadimplente ||
          this.isDownloading
        )
          return;

        const municipio =
          analiseStore.analise?.unidade_gestora?.cidade ||
          "município desconhecido";

        useDownloadFile({
          url: `${location.origin}/api/sapc/analise/${analiseId}/download`,
          fileName: `Prestação de Contas de Governo – Prefeitura Municipal de ${municipio}.pdf`
        });
      },
      async atualizarMarcadores() {
        this.atualizandoReferencias = true;
        try {
          const response = await SAPCApi.gerarReferencias(this.analise.id);

          if (response.data.success) {
            Toaster.toast({
              message: "Marcadores de referências atualizados com sucesso!",
              status: "success"
            });
          } else {
            Toaster.toast({
              message: response.data.message || "Erro ao atualizar marcadores",
              status: "warn"
            });
          }
        } catch (error) {
          console.error("Erro ao atualizar marcadores:", error);
          Toaster.toast({
            message: "Erro ao atualizar marcadores de referências",
            status: "error"
          });
        } finally {
          this.atualizandoReferencias = false;
        }
      },
      handleError(error) {
        if (error) {
          const status = error?.status ?? error.data.status;
          const message = error?.message ?? error.data.message;

          Toaster.toast({
            message,
            status: status ?? "warn"
          });
        }
      }
    }
  });
</script>

<style lang="scss" scoped>
  // showAll switch
  #top-actions {
    label {
      font-size: 14px;
      top: 2px;
    }
    .mdi {
      margin-right: 0;
    }
  }
</style>
<style lang="scss">
  #table-remessas-exercicio {
    .v-data-table-header__content {
      font-weight: 700;
    }
  }
</style>
