import axios from "axios";

const fetchUnidadesGestoras = async function (pagination, filter) {
  const response = await axios.get("/portal-auditor/unidadesGestoras", {
    params: { ...pagination, ...filter }
  });
  return response;
};

const fetchAnalises = async function (pagination, filter) {
  const response = await axios.get("/sapc/analises", {
    params: { ...pagination, ...filter }
  });
  return response;
};

const fetchAnalisesFinalizadas = async function (pagination, filter) {
  const response = await axios.get("/sapc/analises-finalizadas", {
    params: { ...pagination, ...filter }
  });
  return response;
};

const fetchAnalisesNaoFinalizadas = async function (pagination, filter) {
  const response = await axios.get("/sapc/analises-nao-finalizadas", {
    params: { ...pagination, ...filter }
  });
  return response;
};

const fetchFilters = async function (params) {
  const response = await axios.get("/portal-auditor/UGFilterOptions", {
    params: { ...params }
  });
  return response;
};

const fetchAnalise = async function (analiseId) {
  const response = await axios.get(`/sapc/analise/${analiseId}`);
  return response;
};

const fetchFormularios = async function (analiseId) {
  const response = await axios.get(`/sapc/analise/${analiseId}/formularios`);
  return response;
};

const fetchRemessasExercicio = async function (analiseId) {
  const response = await axios.get(`/sapc/analise/${analiseId}/remessas`);
  return response;
};

const fetchFiltersAuditor = async function () {
  const response = await axios.get("/portal-auditor/filtersAuditor");
  return response;
};

const fetchAnaliseFormulario = async function (analiseId, formularioId) {
  const response = await axios.get(
    "/sapc/analise/" + analiseId + "/formulario/" + formularioId,
    {}
  );
  return response;
};

const fetchAnaliseFormularioVersao = async function (
  analiseId,
  formularioId,
  formularioVersaoId
) {
  const response = await axios.get(
    "/sapc/analise/" +
      analiseId +
      "/formulario/" +
      formularioId +
      "/versao/" +
      formularioVersaoId,
    {}
  );
  return response;
};

const saveAnaliseFormulario = async function (analiseFormulario) {
  const response = await axios.post(
    "/sapc/analise/" + analiseFormulario.formularioId + "/formulario",
    analiseFormulario
  );
  return response;
};

const reabrirAnaliseFormulario = async function (analiseFormulario) {
  const response = await axios.post(
    "/sapc/analise/" +
      analiseFormulario.analiseId +
      "/formulario/" +
      analiseFormulario.formularioId +
      "/reabrir"
  );
  return response;
};

const fetchAnexos = function (pagination, filter) {
  return axios.get(
    "/sapc/analise/" +
      filter.analiseId +
      "/formulario/" +
      filter.formularioId +
      "/anexos",
    {
      params: { ...pagination, ...filter }
    }
  );
};

const fetchVersoes = async function (pagination, filter) {
  const response = await axios.get(
    "/sapc/analise/" +
      filter.analiseId +
      "/formulario/" +
      filter.formularioId +
      "/versoes",
    {
      params: { ...pagination, ...filter }
    }
  );
  return response;
};

const fetchHistorico = async function (pagination, filter) {
  const response = await axios.get(
    "/sapc/analise/" +
      filter.analiseId +
      "/formulario/" +
      filter.formularioId +
      "/acoes",
    {
      params: { ...pagination, ...filter }
    }
  );
  return response;
};

const uploadAnexo = async function (
  analiseId,
  analiseFormularioId,
  file,
  descricao
) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("descricao", descricao);

  const response = await axios.post(
    `/sapc/analise/${analiseId}/formulario/${analiseFormularioId}/upload`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
  return response;
};

const downloadAnexo = async function (analiseId, analiseFormularioId, anexoId) {
  const baseUrl = axios.defaults.baseURL;
  const downloadUrl = `${baseUrl}/sapc/analise/${analiseId}/formulario/${analiseFormularioId}/anexo/${anexoId}/download`;

  const link = document.createElement("a");
  document.body.appendChild(link);
  link.href = downloadUrl;
  link.click();
};

const excluirAnexo = async function (anexoId) {
  const deleteUrl = `/sapc/analise/anexo/${anexoId}/excluir`;
  const response = await axios.delete(deleteUrl);
  if (response.status !== 200) {
    console.error(
      "Falha ao excluir o anexo. Código de status:",
      response.status
    );
  }
  return response;
};

const restoreFormularioVersao = async function (
  analiseId,
  formularioId,
  formularioVersaoId
) {
  const response = await axios.get(
    "/sapc/analise/" +
      analiseId +
      "/formulario/" +
      formularioId +
      "/versao/" +
      formularioVersaoId +
      "/restore",
    {}
  );
  return response;
};

const updateProtocoloETCE = async function(analiseId, protocolo_etce){
  const response = await axios.patch(`/sapc/analise/${analiseId}/protocolo-etce`, {
    protocolo_etce
  });

  return response;
}

const gerarReferencias = async function(analiseId) {
  const response = await axios.get(`/sapc/analise/${analiseId}/gerar-referencias`);
  return response;
}

export default {
  fetchUnidadesGestoras,
  fetchFilters,
  fetchFiltersAuditor,
  fetchAnalises,
  fetchAnalisesFinalizadas,
  fetchAnalisesNaoFinalizadas,
  fetchAnalise,
  fetchAnaliseFormulario,
  fetchAnaliseFormularioVersao,
  fetchFormularios,
  fetchRemessasExercicio,
  fetchAnexos,
  fetchVersoes,
  fetchHistorico,
  saveAnaliseFormulario,
  reabrirAnaliseFormulario,
  uploadAnexo,
  downloadAnexo,
  excluirAnexo,
  restoreFormularioVersao,
  updateProtocoloETCE,
  gerarReferencias
};
