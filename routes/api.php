<?php

use App\Http\Controllers\ComunicacaoController;
use App\Http\Controllers\NotificacaoController;
use App\Http\Controllers\Reports\HistoricoEnvioController;
use App\Http\Controllers\SapcController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['jurisdicionado'])->group(function () {
    Route::prefix('periodo-remessa')->group(function () {
        Route::get('exercicio/{exercicio}', 'ApiController@listPeriodoRemessaByExercise');
    });
});

Route::prefix('remessa')->group(function () {
    Route::get('/delivery-report', 'RemessaController@deliveryReport')->middleware('cacheResponse');
    Route::get('/export-excel', 'RemessaController@exportExcel');
    Route::get('/export-pdf', 'RemessaController@exportPDF');
    Route::get('/{remessa}/delivery-protocol', 'RemessaController@protocol');

    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/countersDashboard', 'RemessaController@countersDashboard');
        Route::get('/createRemittanceInContinuosPeriod/{unidadeGestora}/{periodo}', 'RemessaController@createRemittanceInContinuosPeriod');
        Route::get('/conclusao', 'RemessaController@conclusao');
        Route::get('/conclusaoContabil', 'RemessaController@conclusaoContabil');
        Route::delete('/deleteRemessaContinua/{remessaParcial}', 'RemessaController@deleteRemessaContinua');
        Route::get('/enviosAbertos', 'RemessaController@enviosAbertos');
        Route::get('/filters', 'RemessaController@filters');
        Route::get('/filtersAuditor', 'RemessaController@filtersAuditor');
        Route::get('/inadimplentes', 'RemessaController@inadimplentes');
        Route::get('/list', 'RemessaController@list');
        Route::get('/retificacao', 'RemessaController@retificacao');
        Route::get('/testeRegra', 'RemessaController@testeRegra');
        Route::get('/{remessa}/protocol', 'RemessaController@protocol');
    });
});

Route::group(['prefix' => 'historico'], function ($router) {
    Route::get('/', [HistoricoEnvioController::class, 'export']);
});

Route::group(['prefix' => 'remessas'], function ($router) {
    Route::get('/filters', 'Reports\RemittanceFilterController@filters')->middleware('cacheResponse');
});

Route::group(['prefix' => 'reports'], function ($router) {
    Route::get('/filters', 'Reports\RemittanceFilterController@filters')->middleware('cacheResponse');
    Route::get('/tcu/folha-pagamento-download', 'Reports\TcuController@folhaPagamentoDownload');
    Route::get('/infocontas/folha-pagamento-download', 'Reports\InfocontasController@folhaPagamentoInfocontasDownload');
});

Route::prefix('unidadesgestoras')->group(function () {
    Route::middleware(['auth'])->group(function () {
        Route::get('/UGFilterOptions', 'UnidadeGestoraController@filterOptions');
    });
    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/', 'UnidadeGestoraController@index');
        Route::get('/show/{unidadeGestora}', 'UnidadeGestoraController@show');
    });
});

Route::prefix('remessaParcial')->group(function () {
    // Rotas que não precisam de middleware
    Route::get('/{remessa}/admin-download', 'RemessaParcialController@downloadAdmin')->name('remessa_download_admin');
    Route::get('/{remessa}/admin-download-excel', 'RemessaParcialController@downloadExcelAdmin')->name('remessa_download_excel_admin');
    Route::get('/{remessa}/admin-download-inconsistencies', 'RemessaParcialController@downloadInconsistencies')->name('remessa_download_inconsistencies_admin');
    Route::get('/{remessa}/admin-receipt', 'RemessaParcialController@receipt')->name('remessa_download_pdf_admin_receipt');
    Route::get('/{remessa}/historico/{historico}/download', 'RemessaParcialController@historicoRemessaErroDownload')->name('historico_remessa_erro_download');
    Route::get('/{remessa}/exports/historico/errors-to-pdf', 'RemessaParcialController@exportErrorsXSDToPDF')->name('download_remessa_erros_validacao_historico_pdf');

    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/createRemittanceInContinuosPeriod', 'RemessaParcialController@createRemittanceInContinuosPeriod');
        Route::get('/historico/{remessa}', 'RemessaParcialController@historico');
        Route::get('/{remessa}', 'RemessaParcialController@show')->whereNumber('remessa');
        Route::get('/{remessa}/process/validation-result', 'RemessaParcialController@getValidationResult')->whereNumber('remessa');
        Route::get('/{remessa}/download', 'RemessaParcialController@download')->name('remessa_download');
        Route::get('/{remessa}/download-excel', 'RemessaParcialController@downloadExcel')->name('remessa_download_excel');
        Route::get('/{remessa}/download-inconsistencies', 'RemessaParcialController@downloadInconsistencies')->name('remessa_download_inconsistencies');
        Route::get('/{remessa}/receipt', 'RemessaParcialController@receipt');
        Route::post('/{remessa}/removeSignature', 'RemessaParcialController@removeSignature');
        Route::post('/{remessaParcial}/upload', 'RemessaParcialController@upload');
        Route::get('/{remessa}/export-errors-xsd-to-pdf', 'RemessaParcialController@exportErrorsRemessasXSDToPDF');
        Route::get('/{remessa}/export-errors-xsd-to-excel', 'RemessaParcialController@exportErrorsXSDToExcel');
        Route::put('/{remessa}/reopen', 'RemessaParcialController@reopen');
        Route::post('/{remessa}/sign', 'RemessaParcialController@sign');
        Route::get('/inconsistencias/remessa/{remessa}', 'RemessaParcialController@getInconsistenciesData');
        Route::post('/{remessa}/cancel', 'RemessaParcialController@cancel');
    });

    Route::middleware(['portal'])->group(function () {
        Route::get('/balancete/{parcial}/export-to-pdf', 'BalanceteController@exportToPDF');
        Route::get('/token', 'RemessaParcialController@getToken');
    });
});

Route::prefix('certidao')->group(function () {
    Route::get('/{certidao}/download-pdf', 'CertidaoController@downloadPdf');
});

Route::prefix('periodoremessa')->group(function () {
    Route::get('/get-filtros', 'PeriodoRemessaController@getFiltros');
    Route::get('/listPortal', 'PeriodoRemessaController@listPortal');
    Route::get('/getTodosExercicios', 'PeriodoRemessaController@getTodosExercicios');

    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/', 'PeriodoRemessaController@list');
        Route::get('/continuousRemittance', 'PeriodoRemessaController@continuousRemittance');
        Route::get('/continuousRemittance/{exercicio}/unidade-gestora/{unidade_gestora}', 'PeriodoRemessaController@continuousRemittanceByExerciseAndUG');
        Route::get('/get-exercicios', 'PeriodoRemessaController@getExercicios');
        Route::get('/get-remessas/{exercicio}', 'PeriodoRemessaController@getRemessasPorExercicio');
        Route::get('/get-tipo-layout-continuous-remittance/{exercicio}', 'PeriodoRemessaController@getTipoLayoutContinuousRemittance');
        Route::get('/list', 'PeriodoRemessaController@list');
        Route::get('/listTable', 'PeriodoRemessaController@listTable');
    });
});

Route::prefix('lacuna')->group(function () {
    Route::get('token', 'Auth\\LacunaLoginController@issueToken');
});

Route::prefix('auth')->group(function () {
    Route::post('login', 'Auth\\LacunaLoginController@login');

    Route::middleware(['portal'])->group(function () {
        Route::get('user', 'UserController@me');
    });

    Route::middleware(['jurisdicionado'])->group(function () {
        Route::post('logout', 'Auth\\LacunaLoginController@logout');
    });
});

Route::prefix('users')->group(function () {
    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/', 'UserController@index');
        Route::post('/impersonate', 'UserController@impersonate');
        Route::post('/findUser', 'UserController@findUser');
        Route::get('/unidades-gestoras/me', 'UserController@getUnidadesGestoras');
    });
});

Route::prefix('responsavel')->group(function () {
    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/{cpf}', 'CardugMockController@responsavel');
    });
});

Route::prefix('layouts')->group(function () {
    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/', 'LayoutController@index');
        Route::get('/{layout_id}/generate-xsd', 'LayoutController@generateXSD');
        Route::get('/download-zip', 'LayoutController@downloadZip');
    });
});

Route::prefix('regras-integridade')->group(function () {
    Route::middleware(['portal'])->group(function () {
        Route::get('/inconsistencias/{parcial}', 'RegrasIntegridadeController@inconsistenciasContabeisPorRemessaParcial');
    });
});

Route::prefix('contador')->group(function () {
    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/filterOptions', 'UnidadeGestoraController@filterOptions');
        Route::get('/unidadesgestoras', 'UnidadeGestoraController@index');
    });

    Route::middleware(['portal'])->group(function () {
        Route::get('/exportDemonstracaoToPDF/{demonstracao}/{unidadeGestoraWithCidade}/{anoExercicio}/export-demonstracao-to-pdf', 'ContadorController@exportDemonstracaoToPDF');
        Route::get('/exportDemonstracaoToXLS/{demonstracao}/{unidadeGestoraWithCidade}/{anoExercicio}/export-demonstracao-to-xls', 'ContadorController@exportDemonstracaoToXLS');

        Route::get('/getdcasp/{demonstracao}/{unidadeGestoraWithCidade}/{anoExercicio}', 'ContadorController@getDcasp');
        Route::get('/getDcaspLinhas/{anexo}/{unidadeGestoraWithCidade}', 'ContadorController@getDcaspLinhas');
        Route::get('/inconsistencia/{parcial}/export-to-pdf', 'RegrasIntegridadeController@exportToPDF');
        Route::get('/{parcial}', 'ContadorController@balanceCheck');
    });
});

Route::prefix('portal-auditor')->group(function () {
    Route::middleware(['auditor'])->group(function () {
        Route::get('/', 'UserController@index');
        Route::get('/conclusaoContabil', 'RemessaController@auditorConclusaoContabil');
        Route::get('/filterOptions', 'RemessaController@filterOptions');
        Route::get('/filtersAuditor', 'RemessaController@filtersAuditor');
        Route::get('/inconsistencia/export-to-pdf-with-filter', 'RegrasIntegridadeController@exportInconsistenciasPdf');
        Route::get('/inconsistencias-contabeis', 'AuditorController@inconsistenciasContabeis');
        Route::get('/inconsistencias-contabeis-detail/{regra}/{unidadeGestora}/{periodoRemessa}', 'AuditorController@inconsistenciasContabeisDetail');
        Route::get('/unidadesGestoras', 'UnidadeGestoraController@auditorIndex');
        Route::get('user', 'UserController@me');
        Route::get('/UGFilterOptions', 'UnidadeGestoraController@filterOptions');
        Route::post('/findUser', 'UserController@findUser');
        Route::post('/impersonate', 'UserController@impersonate');
        Route::post('/comunicacao/mensagens', [ComunicacaoController::class, 'listMensagensAuditor']);
    });
});

Route::prefix('sapc')->group(function () {
    Route::middleware(['auditor'])->group(function () {
        Route::get('/analises', [SapcController::class, 'analisesTodas']);
        Route::get('/analises-nao-finalizadas', [SapcController::class, 'analisesNaoFinalizadas']);
        Route::get('/analises-finalizadas', [SapcController::class, 'analisesFinalizadas']);
    });

    Route::prefix('analise')->group(function () {
        Route::middleware('managerAnalise')->group(function () {
            Route::get('/{analise}', [SapcController::class, 'analiseShow']);
            Route::get('/{analise}/formulario/{analiseFormulario}', [SapcController::class, 'formularioShow']);
            Route::post('/{analise}/formulario/{analiseFormulario}/reabrir', [SapcController::class, 'formularioReabrir']);
            Route::post('/{analise}/formulario/{analiseFormulario}/upload', [SapcController::class, 'anexoUpload']);
            Route::post('/{analise}/formulario/{analiseFormulario}/log', [SapcController::class, 'formularioLogAcao']);
            Route::get('/{analise}/formulario/{analiseFormulario}/acoes', [SapcController::class, 'formularioGetAcoes']);
            Route::get('/{analise}/formulario/{analiseFormulario}/anexo/{anexo}/download', [SapcController::class, 'anexoDownload'])->name('anexo.download');
            Route::get('/{analise}/formularios', [SapcController::class, 'analiseFormularios']);
            Route::get('/{analise}/formulario/{analiseFormulario}', [SapcController::class, 'formularioShow']);

            Route::get('/{analise}/formulario/{analiseFormulario}/versao/{analiseVersaoFormulario}/restore', [SapcController::class, 'formularioRestoreVersao']);
            Route::get('/{analise}/formulario/{analiseFormulario}/versao/{analiseVersaoFormulario}', [SapcController::class, 'formularioGetVersao']);
            Route::get('/{analise}/formulario/{analiseFormulario}/anexos', [SapcController::class, 'anexoIndex']);
            Route::get('/{analise}/formulario/{analiseFormulario}/versoes', [SapcController::class, 'formularioGetVersoes']);

            Route::delete('/anexo/{anexo}/excluir', [SapcController::class, 'anexoDestroy']);
            Route::post('/{analiseFormulario}/formulario', [SapcController::class, 'formularioStore']);

            Route::patch('/{analise}/protocolo-etce', [SapcController::class, 'updateProtocoloEtce']);
            Route::post('/{analise}/atualizar-marcadores', [SapcController::class, 'atualizarMarcadores']);
        });

        Route::put('/{analise}/formulario/{analiseFormulario}/auto-save', [SapcController::class, 'autoSaveAnalise']);
        Route::get('/{analise}/remessas', [SapcController::class, 'analiseRemessas']);
        Route::get('/{analise}/download', [SapcController::class, 'analiseDownload'])->name('analise.download');
    });
});

Route::prefix('layouts')->group(function () {
    Route::get('/indexPortal', 'LayoutController@indexPortal');
    Route::get('/{layout_id}/generate-xsd-portal', 'LayoutController@generateXSDPortal');
    Route::get('/download-zip-portal', 'LayoutController@downloadZipPortal');
});

Route::prefix('duvidas-frequentes')->group(function () {
    Route::get('/', 'DuvidaFrequenteController@index');
});

Route::prefix('unidadesgestoras-reports')->group(function () {
    Route::post('/', 'UnidadeGestoraController@list');
});

Route::prefix('questionario')->group(function () {
    Route::middleware(['jurisdicionado'])->group(function () {
        Route::get('/', 'QuestionarioController@list');
        Route::post('/adicionar-respostas', 'QuestionarioController@adicionarRespostas');
        Route::get('/filters', 'QuestionarioController@getFilters');
        Route::get('/historico/{respostaQuestionariosPorUG}', 'QuestionarioController@historico');
        Route::get('/{respostaQuestionariosPorUG}/export-pdf', 'QuestionarioController@exportPdf');
        Route::get('/{respostaQuestionariosPorUG}/protocol', 'QuestionarioController@protocol');
        Route::post('/{respostaQuestionarioUG}/removeSignature', 'QuestionarioController@removeSignature');
        Route::get('/{questionario}', 'QuestionarioController@show')->whereNumber('questionario');

        Route::prefix('perguntas')->middleware(['responderQuestionario'])->group(function () {
            Route::get('/{respostaQuestionariosPorUG}', 'QuestionarioController@perguntas');
        });
    });
});

Route::prefix('questionario-admin')->group(function () {
    Route::middleware(['auth'])->group(function () {
        Route::get('/{respostaQuestionariosPorUG}/export-pdf', 'QuestionarioController@exportPdf');
    });
});

Route::prefix('portal-jurisdicionados')->group(function () {
    Route::get('/categorias', 'PortalJurisdicionadosController@listCategorias');
    Route::get('/download/{postagemId}', 'PortalJurisdicionadosController@download')->whereNumber('id');
    Route::get('/postagens', 'PortalJurisdicionadosController@listPostagens');
});

Route::prefix('comunicacao')->group(function () {
    Route::post('/mensagens/send', [ComunicacaoController::class, 'sendMensagem']);
    Route::post('/mensagens/rascunho/{mensagemRascunho}/autosave', [ComunicacaoController::class, 'autosaveMensagem']);
    Route::post('/mensagens/{mensagem}/respostas/{resposta}/autosave', [ComunicacaoController::class, 'autosaveResposta']);
    Route::post('/mensagens/rascunho/send', [ComunicacaoController::class, 'sendRascunho']);
    Route::post('/mensagens/anexos/upload', [ComunicacaoController::class, 'uploadArquivo']);
    Route::get('/mensagens/anexos/download/{arquivo}', [ComunicacaoController::class, 'downloadArquivo']);
    Route::delete('/mensagens/anexos/temporarios/{arquivo}', [ComunicacaoController::class, 'deletaArquivoTemporario']);

    Route::middleware(['portal'])->group(function () {
        Route::post('/mensagens/summary', [ComunicacaoController::class, 'listSummaryMensagem']);
        Route::post('/mensagens', [ComunicacaoController::class, 'listMensagens']);
    });

    Route::get('/mensagens/{mensagem}/anexos/{anexo}/download-anexo', [ComunicacaoController::class, 'downloadAnexo']);
    Route::post('/mensagens/{mensagemId}/anexos', [ComunicacaoController::class, 'listAnexos']);
    Route::get('/mensagens/{mensagemId}/respostas', [ComunicacaoController::class, 'listRespostasEAnexos']);
    Route::patch('/mensagens/{mensagem}/marcar-mensagem-como-lida', [ComunicacaoController::class, 'putMensagemLida']);
    Route::patch('/mensagens/{mensagem}/respostas/{resposta}/marcar-resposta-como-lida', [ComunicacaoController::class, 'putRespostaLida']);
    Route::delete('/mensagens/{mensagem}/respostas/{resposta}', [ComunicacaoController::class, 'deleteResposta']);
    Route::patch('/mensagens/{mensagem}/finalize-mensagem', [ComunicacaoController::class, 'finalizeMensagem']);
    Route::post('/mensagens/{mensagem}/anexos/upload', [ComunicacaoController::class, 'uploadAnexo']);
    Route::put('/mensagens/{mensagem}/respostas/{resposta}/salvar', [ComunicacaoController::class, 'updateResposta']);
    Route::post('/mensagens/{mensagem}/respostas/salvar', [ComunicacaoController::class, 'sendResposta']);
    Route::get('/prioridades', [ComunicacaoController::class, 'listPrioridades']);
    Route::get('/statuses', [ComunicacaoController::class, 'listMensagemStatuses']);
    Route::get('/diretorias', [ComunicacaoController::class, 'listDiretorias']);
    Route::get('/tipo-mensagem', [ComunicacaoController::class, 'listTipoMensagem']);
    Route::patch('/mensagens/{mensagem}/respostas/{resposta}/draft-resposta', [ComunicacaoController::class, 'draftResposta']);
    Route::get('/mensagens/{mensagemId}/list-detalhe-mensagem', [ComunicacaoController::class, 'listDetalheMensagem']);
    Route::get('/mensagens/{mensagemId}/respostas/list-autosave', [ComunicacaoController::class, 'listRespostasAutosave']);
    Route::get('/mensagens/{mensagem}/download-anexos', [ComunicacaoController::class, 'downloadAllAnexos']);
    Route::delete('/mensagens/{mensagem}/anexos/{anexo}', [ComunicacaoController::class, 'deleteAnexo']);
});

Route::prefix('select')->group(function () {
    Route::get('/managementUnits', 'PeriodoRemessaController@managementUnitOptions');
});

Route::prefix('notificacao')->group(function () {
    Route::middleware(['portal'])->group(function () {
        Route::get('/list-categorias', [NotificacaoController::class, 'listarCategorias']);
        Route::patch('/{notificacao}/mark-as-read', [NotificacaoController::class, 'marcarNotificacaoComoLida']);
        Route::get('/list-all-unread-notification', [NotificacaoController::class, 'listarNotificacoesNaoLidas']);
        Route::post('/list-all-notification', [NotificacaoController::class, 'listarTodasNotificacoes']);
        Route::post('/mark-notifications-as-read', [NotificacaoController::class, 'marcarNotificacoesComoLidas']);
    });
});

Route::controller('ApiController')->middleware(['portal'])->group(function () {
    Route::get('/exercicios', 'fetchExercicios');
    Route::get('/tipos-unidade', 'fetchTiposUnidade');
    Route::get('/municipios', 'fetchMunicipios');
    Route::get('/unidades-gestoras/admin', 'fetchAllUnidadesGestoras');
    Route::get('/unidades-gestoras', 'fetchUnidadesGestoras');
    Route::get('/periodos-remessa', 'fetchPeriodosRemessa');
    Route::get('/relatorias', 'fetchRelatorias');
    Route::get('/esferas', 'fetchEsferas');
    Route::get('/poderes', 'fetchPoderes');
    Route::get('/grupos-regionais', 'fetchGruposRegionais');
    Route::get('/tipos-administracao', 'fetchTiposAdministracao');
    Route::get('/subtipos-unidades', 'fetchSubtiposUnidades');
    Route::get('/diretorias', 'fetchDiretorias');
    Route::get('/comportamentos', 'fetchComportamentos');
    Route::get('/tipos-layouts', 'fetchTiposLayouts');
    Route::get('/naturezas-lancamento', 'fetchNaturezasLancamento');
    Route::get('/periodo-remessa/status', 'fetchPeriodoRemessaStatus');
    Route::prefix('questionario')->group(function () {
        Route::get('/exercicios', 'fetchQuestionarioExercicios');
        Route::get('/tipos', 'fetchQuestionarioTipos');
        Route::get('/status', 'fetchQuestionarioStatus');
    });
});

Route::any('{catchall}', function () {
    return response()->json([
        'status' => 'error',
        'message' => 'Endpoint não encontrado ou parâmetro inválido.',
    ], 404);
})->where('catchall', '.*');
